<?php

namespace app\api\controller;

use app\api\service\RankingUtil;
use app\api\service\Util;
use app\common\Gyration;
use app\common\NetDiskService;
use think\Console;
use think\Controller;
use think\Db;

class Wechat extends Controller
{
    /**
     * 配置详情
     */
    public function get_account()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('033cf8d1-632c-806b-575d-b77ec7f7bdfd', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '未开通此插件！']);
        }
        $app_info = Db::name('wx_popular')->where('much_id', $data['much_id'])->field('wx_app_qrcode,wx_app_name,wx_app_id')->find();
        if (empty($app_info)) {
            return $this->json_rewrite(['code' => 1, 'msg' => '未配置！']);
        }
        //查询是否已经绑定
        $user_info = Db::name('user')->where('user_wechat_open_id', $data['openid'])->where('much_id', $data['much_id'])->find();
        $wx_popular = Db::name('wx_popular_bind_user')->where('user_id', $user_info['id'])->where('much_id', $data['much_id'])->count();
        $app_info['popular'] = $wx_popular;
        $app_info['wx_app_id'] = authcode($app_info['wx_app_id'], 'DECODE', 'YuLuoMassInfo');;
        return $this->json_rewrite(['code' => 0, 'info' => $app_info]);
    }

    /**
     * 解绑
     */
    public function clean_account()
    {
        $data = input('param.');
        $user_info = Db::name('user')->where('user_wechat_open_id', $data['openid'])->where('much_id', $data['much_id'])->find();
        $wx_popular = Db::name('wx_popular_bind_user')->where('user_id', $user_info['id'])->where('much_id', $data['much_id'])->count();
        if ($wx_popular == 0) {
            return $this->json_rewrite(['code' => 0, 'msg' => '解绑成功！']);
        } else {
            $res = Db::name('wx_popular_bind_user')->where('user_id', $user_info['id'])->where('much_id', $data['much_id'])->delete();
            if (!$res) {
                return $this->json_rewrite(['code' => 1, 'msg' => '解绑失败！']);
            } else {
                return $this->json_rewrite(['code' => 0, 'msg' => '解绑成功！']);
            }
        }
    }

    /**
     * 外部网页授权
     */
    public function wx_open_id()
    {
        $data = input('param.');
        $code = $data['code'];
        $much_id = $data['state'];
        //查询公众号信息
        $app_info = Db::name('wx_popular')->where('much_id', $much_id)->find();
        $post = new Gyration();
        if (empty($app_info)) {
            $this->assign('id', 2);
        }
        $appid = authcode($app_info['wx_app_id'], 'DECODE', 'YuLuoMassInfo');
        $secret = authcode($app_info['wx_app_secret'], 'DECODE', 'YuLuoMassInfo');
        //获取网页授权acctoken
        $url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={$appid}&secret={$secret}&code={$code}&grant_type=authorization_code";
        $res = json_decode($post->_requestGet($url), true);
        if ($res['errcode'] > 0) {
            $this->assign('id', $res['errcode']);
            $this->assign('app_info', $app_info);
            return $this->fetch();
        }
        $openid = $res['openid'];
        //查询是否存在
        $check = Db::name('wx_popular_bind_user')->where('open_id', $openid)->where('much_id', $much_id)->find();
        if (empty($check)) {
            $res = Db::name('wx_popular_bind_user')->insert(['union_id' => $res['unionid'], 'user_id' => 0, 'open_id' => $openid, 'bind_time' => time(), 'much_id' => $much_id]);
            if (!$res) {
                $this->assign('id', 1);
                $this->assign('app_info', $app_info);
            }
        } else {
            if (empty($check['union_id'])) {
                $res = Db::name('wx_popular_bind_user')->where('id', $check['id'])->update(['union_id' => $res['unionid']]);
                if (!$res) {
                    $this->assign('id', 1);
                    $this->assign('app_info', $app_info);
                }
            }
        }
        $we = new Util();
        $xcx_acctoken = $we->getWchatAcctoken($much_id);
        $url_like = "https://api.weixin.qq.com/wxa/generatescheme?access_token=" . $xcx_acctoken;
        //$data=['jump_wxa'=>['path'=>'/yl_welore/pages/index/index','query'=>'login']];
        $generatescheme = json_decode($post->_requestPost($url_like, ''), true);
        if ($generatescheme['errcode'] > 0) {
            $this->assign('id', $res['errcode']);
            $this->assign('app_info', $app_info);
            return $this->fetch();
        } else {
            $this->assign('id', $res['errcode']);
            $app_info['url_scheme'] = $generatescheme['openlink'];
            $this->assign('app_info', $app_info);
        }
        $this->assign('id', 0);
        $this->assign('app_info', $app_info);

        return $this->fetch();
    }

    /**
     * 小程序发起本地上传
     */
    public function local_upload()
    {
        $data = input('param.');
        //判断当前用户
        $user=Db::name('user')->where('token',$data['token'])->where('user_wechat_open_id',$data['open_id'])->where('much_id',$data['much_id'])->find();
        if(empty($user)){
            $this->assign('key', 1);
            $this->assign('msg', '用户信息获取失败！');
            return $this->fetch();
        }
        //  允许的上传类型
        $allowedUploadTypes = NetDiskService::allowedUploadTypes($data['much_id']);
        $this->assign('allowedUploadTypes', $allowedUploadTypes);
        $this->assign('uid', $user['id']);
        $this->assign('pid', $data['pid']);
        $this->assign('much_id', $data['much_id']);
        $this->assign('key', 0);
        $this->assign('msg', '点击按钮上传文件！');
        return $this->fetch();
    }

    /*
    * 上传文件
    */
    public function uploadNetDisc()
    {
        if (request()->isPost() && request()->isAjax()) {
            //  获取文件
            $file = request()->file('netDiscFile');
            //  获取文件大小
            $fileGetSize = $file->getSize();
            //  获取文件名称
            $fileName = pathinfo($file->getInfo('name'), PATHINFO_FILENAME);
            //  用户用户编号
            $uid = input('post.uid', 0);
            //  获取目录编号
            $pid = input('post.pid', 0);
            //  获取多用户标识
            $much_id = input('post.much_id', 0);

            //查询网盘配置
            $netdisc_config = Db::name('netdisc_config')->where('much_id', $much_id)->find();
            if (bccomp($fileGetSize, $netdisc_config['upload_size_limit']) == 1) {
                return json(['code' => 0, 'msg' => '上传限制' . $this->setupSize($netdisc_config['upload_size_limit'])]);
            }
            $rank = new RankingUtil();
            //判断用户网盘容量
            $user_big = $rank->get_my_netdisc_big(['much_id' => $much_id, 'user_id' => $uid]);
            //已使用
            $user_use = $rank->get_my_netdisc_use(['much_id' => $much_id, 'user_id' => $uid]);
            $zong = bcadd($user_use, $fileGetSize);
            if (bccomp($zong, $user_big) == 1) {
                return json(['code' => 0, 'msg' => '网盘容量不足']);
            }
            //  获取上传结果
            $upRes = NetDiskService::manipulate($file, $much_id);
            //  判断是否上传成功
            if ($upRes['code'] > 0) {
                Db::startTrans();
                try {
                    //  新上传的文件
                    if (intval($upRes['ncId']) === 0) {
                        $upRes['ncId'] = Db::name('netdisc')->insertGetId([
                            'file_type' => $upRes['fileType'],
                            'file_md5' => $upRes['fileMD5'],
                            'file_suffix' => $upRes['getExt'],
                            'file_size' => $upRes['getSize'],
                            'file_address' => $upRes['url'],
                            'file_status' => 1,
                            'up_user_id' => $uid,
                            'up_user_ip' => request()->ip(),
                            'add_time' => time(),
                            'much_id' => $much_id,
                        ]);
                    }
                    //  保存至用户网盘
                    Db::name('netdisc_belong')->insert([
                        'nc_id' => $upRes['ncId'],
                        'user_id' => $uid,
                        'file_name' => $fileName,
                        'parent_path_id' => $pid,
                        'is_dir' => 0,
                        'add_time' => time(),
                        'is_sell' => 1,
                        'is_del' => 0,
                        'much_id' => $much_id
                    ]);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
                return json(['code' => 1, 'msg' => 'success']);
            } else {
                return json(['code' => 0, 'msg' => $upRes['msg']]);
            }
        }
    }

    public function setupSize($fileSize)
    {
        $size = sprintf("%u", $fileSize);
        if ($size == 0) {
            return ("0 Bytes");
        }
        $sizename = array(" B", " KB", " MB", " GB", " TB", " PB", " EB", " ZB", " YB");
        return round($size / pow(1024, ($i = floor(log($size, 1024)))), 2) . $sizename[$i];
    }
    /**
     * 小程序内授权
     */
    public function index()
    {
        $data = input('param.');
        $plug = new Plugunit();
        $ste = explode("A", $data['state']);
        $uid = $ste[0];
        $much_id = $ste[1];
        if (!$plug->check_plug('033cf8d1-632c-806b-575d-b77ec7f7bdfd', $much_id)) {
            $this->assign('id', 2);
            return $this->fetch();
        }

        $code = $data['code'];
        //查询当前用户是否绑定公众号
        $post = new Gyration();
        $check = Db::name('wx_popular_bind_user')->where('user_id', $uid)->where('much_id', $much_id)->find();
        //查询公众号信息
        $app_info = Db::name('wx_popular')->where('much_id', $much_id)->find();
        if (empty($app_info)) {
            $this->assign('id', 2);
        }
        $appid = authcode($app_info['wx_app_id'], 'DECODE', 'YuLuoMassInfo');
        $secret = authcode($app_info['wx_app_secret'], 'DECODE', 'YuLuoMassInfo');
        $url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={$appid}&secret={$secret}&code={$code}&grant_type=authorization_code";
        if (empty($check)) {
            //获取数据
            $res = json_decode($post->_requestGet($url), true);
            if ($res['errcode'] > 0) {
                $this->assign('id', 1);
                return $this->fetch();
            }

            $openid = $res['openid'];
            $res = Db::name('wx_popular_bind_user')->insert(['user_id' => $uid, 'open_id' => $openid, 'bind_time' => time(), 'much_id' => $much_id]);
            if (!$res) {
                $this->assign('id', 1);
            }
            $key = [
                'open_id' => $openid,
                'first' => '已成功绑定公众号模板消息',
                'keyword1' => '绑定',
                'keyword2' => '小程序互动信息不再错过',
                'remark' => '前往小程序',
            ];
            $json = $this->send_template($key, $much_id);

        } else {
            if (empty($check['open_id'])) {
                //获取数据
                $res = json_decode($post->_requestGet($url), true);
                if ($res['errcode'] > 0) {
                    $this->assign('id', 1);
                    return $this->fetch();
                }
                $openid = $res['openid'];
                $res = Db::name('wx_popular_bind_user')->insert(['user_id' => $uid, 'open_id' => $openid, 'bind_time' => time(), 'much_id' => $much_id]);
                if (!$res) {
                    $this->assign('id', 1);
                    return $this->fetch();
                }
            }
            $json['errcode'] = 0;
        }
        if ($json['errcode'] != 0) {
            $this->assign('id', 1);
            $this->assign('msg',$json['errmsg']);
        } else {
            $this->assign('id', 0);
        }
        $this->assign('name', $app_info['wx_app_name']);
        return $this->fetch();
    }

    /**
     * 发送模版消息
     */
    public function send_template($data, $much_id)
    {
        $plug = new Plugunit();
        if (!$plug->check_plug('033cf8d1-632c-806b-575d-b77ec7f7bdfd',$much_id)) {
            return ['code' => 1, 'msg' => '应用未开通此插件！'];
        }
        //获取后台APPID
        $getConfig = cache('fatal_' . $much_id);
        if (!$getConfig) {
            $getConfig = Db::name('config')->where('much_id', $much_id)->find();
            if ($getConfig) {
                foreach ($getConfig as $key => $value) {
                    if ($key != 'id' && $key != 'pay_react' && $key != 'much_id') {
                        $getConfig[$key] = authcode($getConfig[$key], 'DECODE', 'YuluoNetwork', 0);
                    }
                }
                cache('fatal_' . $much_id, $getConfig);
            }
        }
        //查询公众号信息
        $app_info = Db::name('wx_popular')->where('much_id', $much_id)->find();
        //获取要接收的用户open_id
        $access_token = $this->getAccessToken($much_id);
        $url = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" . $access_token;

        $template_id = json_decode($app_info['imagine_data'], true);
        $json = [
            'touser' => $data['open_id'],
            'template_id' => $template_id['YL0001'],
            "miniprogram" => [
                "appid" => $getConfig['app_id'],
                "pagepath" => $data['page']
            ]
        ];
        if(intval($template_id['type'])==1){
            $kk= [
                $template_id['param1'] => ['value' => $data['first'], 'color' => '#333333'],
                $template_id['param2'] => ['value' => $data['keyword1'], 'color' => '#333333'],
                $template_id['param3'] => ['value' => $data['keyword2'], 'color' => '#333333'],
                $template_id['param4']  => ['value' => $data['remark'], 'color' => '#CC0033'],
            ];
        }
        if(empty($template_id['type'])||intval($template_id['type'])==0||!isset($template_id['type'])){
            $kk= [
                $template_id['first'] => ['value' => $data['first'], 'color' => '#333333'],
                $template_id['keyword1'] => ['value' => $data['keyword1'], 'color' => '#333333'],
                $template_id['keyword2'] => ['value' => $data['keyword2'], 'color' => '#333333'],
                $template_id['remark']  => ['value' => $data['remark'], 'color' => '#CC0033'],
            ];
        }
        $json['data'] = $kk;
        $post = new Gyration();
        $res = $post->_requestPost($url, json_encode($json));
        return json_decode($res, true);
    }

    /**
     * 公众号access_token
     */
    public function getAccessToken($much_id)
    {
        $post = new Gyration();
        // access_token 应该全局存储与更新，以下代码以写入到文件中做示例
        $data = Db::name('wx_popular')->where('much_id', $much_id)->find();
        $wx_app_secret = authcode($data['wx_app_secret'], 'DECODE', 'YuLuoMassInfo');
        $wx_app_id = authcode($data['wx_app_id'], 'DECODE', 'YuLuoMassInfo');
        if (empty($data)) {
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" . $wx_app_id . "&secret=" . $wx_app_secret;
            $res = json_decode($post->_requestGet($url), true);
            if (isset($res['errcode'])) {
                return $res;
            }
            $access_token = $res['access_token'];
            $up['token_exp_time'] = time() + 7000;
            $up['access_token'] = $access_token;
            $up['much_id'] = $much_id;
            Db::name('wx_popular')->insert($up);
        } else {
            if ($data['token_exp_time'] < time()) {
                // 如果是企业号用以下URL获取access_token
                // $url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=$this->appId&corpsecret=$this->appSecret";
                $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" . $wx_app_id . "&secret=" . $wx_app_secret;
                $res = json_decode($post->_requestGet($url), true);
                if (isset($res['errcode'])) {
                    return $res;
                }
                $access_token = $res['access_token'];
                if ($access_token) {
                    $up['token_exp_time'] = time() + 7000;
                    $up['access_token'] = $access_token;
                    Db::name('wx_popular')->where('much_id', $much_id)->update($up);
                }
            } else {
                $access_token = $data['access_token'];
            }
        }

        return $access_token;
    }

    /**
     * 列表加密
     */
    public function json_rewrite($arr)
    {
        return base64_encode(rawurlencode(json_encode($arr)));
    }
}