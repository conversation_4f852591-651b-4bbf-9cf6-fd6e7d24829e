<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e9984a57-e3bf-430c-966a-2a35c8f89b62" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings">
    <execution />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/fortuity/application/api/controller/Retrieval.php" root0="FORCE_HIGHLIGHTING" root1="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="2PMdaAiTuUAFkXCJbGQGeu43lR5" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.highlight.mappings&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.highlight.symlinks&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.show.date&quot;: &quot;false&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.show.permissions&quot;: &quot;false&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.show.size&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/微时光开发/后台/web/static/mineIcon/user4&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;通义灵码&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\微时光开发\后台\web\static\mineIcon\user4" />
      <recent name="E:\微时光开发\后台\web\static\mineIcon\user2" />
      <recent name="E:\微时光开发\后台\web\static\mineIcon\user1" />
      <recent name="E:\微时光开发\后台\web\static\examine" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\微时光开发\后台\web\static\applet_icon" />
    </key>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="e9984a57-e3bf-430c-966a-2a35c8f89b62" name="更改" comment="" />
      <created>1683270945814</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1683270945814</updated>
      <workItem from="1683270947089" duration="3394000" />
      <workItem from="1684378343874" duration="7195000" />
      <workItem from="1684479127009" duration="3592000" />
      <workItem from="1684552911697" duration="9000" />
      <workItem from="1684557839419" duration="619000" />
      <workItem from="1685854189939" duration="15071000" />
      <workItem from="1686798547991" duration="3557000" />
      <workItem from="1687180153172" duration="625000" />
      <workItem from="1687335749404" duration="968000" />
      <workItem from="1688015153825" duration="6843000" />
      <workItem from="1688175411286" duration="32000" />
      <workItem from="1688203180914" duration="621000" />
      <workItem from="1688204456703" duration="1247000" />
      <workItem from="1688350980029" duration="1172000" />
      <workItem from="1688462356243" duration="193000" />
      <workItem from="1688610099323" duration="793000" />
      <workItem from="1689062331827" duration="966000" />
      <workItem from="1689732893422" duration="2093000" />
      <workItem from="1689904409819" duration="2200000" />
      <workItem from="1693896424916" duration="6593000" />
      <workItem from="1694484828288" duration="2432000" />
      <workItem from="1694586570398" duration="54000" />
      <workItem from="1694670136314" duration="10196000" />
      <workItem from="1694691779964" duration="6410000" />
      <workItem from="1694742470627" duration="6643000" />
      <workItem from="1695123779380" duration="800000" />
      <workItem from="1695621509452" duration="106000" />
      <workItem from="1695795270821" duration="1108000" />
      <workItem from="1696649909248" duration="12322000" />
      <workItem from="1696831089730" duration="2650000" />
      <workItem from="1696921546273" duration="1300000" />
      <workItem from="1697080827302" duration="13259000" />
      <workItem from="1697162339253" duration="21523000" />
      <workItem from="1697435717962" duration="11022000" />
      <workItem from="1697504243130" duration="24514000" />
      <workItem from="1697679683032" duration="18302000" />
      <workItem from="1697782579393" duration="7205000" />
      <workItem from="1697875235940" duration="135000" />
      <workItem from="1697876272712" duration="1618000" />
      <workItem from="1698027035040" duration="486000" />
      <workItem from="1698113402941" duration="14569000" />
      <workItem from="1698201143854" duration="11105000" />
      <workItem from="1698369680721" duration="4127000" />
      <workItem from="1698628615167" duration="234000" />
      <workItem from="1698801526736" duration="261000" />
      <workItem from="1700038859041" duration="260000" />
      <workItem from="1700554094368" duration="1236000" />
      <workItem from="1700616777780" duration="4423000" />
      <workItem from="1701323598376" duration="91000" />
      <workItem from="1701326943606" duration="611000" />
      <workItem from="1701424395848" duration="533000" />
      <workItem from="1701497652436" duration="712000" />
      <workItem from="1701500596063" duration="631000" />
      <workItem from="1702975240801" duration="625000" />
      <workItem from="1703062291178" duration="1700000" />
      <workItem from="1703649593117" duration="11000" />
      <workItem from="1703663666641" duration="41000" />
      <workItem from="1704182885536" duration="395000" />
      <workItem from="1704416686984" duration="409000" />
      <workItem from="1704434996088" duration="2277000" />
      <workItem from="1704447172105" duration="850000" />
      <workItem from="1704700162695" duration="990000" />
      <workItem from="1704702417683" duration="256000" />
      <workItem from="1704702679483" duration="13000" />
      <workItem from="1704703271058" duration="2607000" />
      <workItem from="1705046187328" duration="370000" />
      <workItem from="1705307448245" duration="1243000" />
      <workItem from="1705387099687" duration="1465000" />
      <workItem from="1705561688457" duration="361000" />
      <workItem from="1705562683400" duration="8973000" />
      <workItem from="1705886907330" duration="5542000" />
      <workItem from="1705997401649" duration="2953000" />
      <workItem from="1708330188851" duration="932000" />
      <workItem from="1709000533059" duration="1182000" />
      <workItem from="1709169425614" duration="4456000" />
      <workItem from="1710550889733" duration="780000" />
      <workItem from="1711090659598" duration="614000" />
      <workItem from="1711430636648" duration="2689000" />
      <workItem from="1711958599600" duration="617000" />
      <workItem from="1712127980613" duration="621000" />
      <workItem from="1712825702431" duration="1678000" />
      <workItem from="1714988835553" duration="1763000" />
      <workItem from="1715049051245" duration="4754000" />
      <workItem from="1715067670995" duration="69000" />
      <workItem from="1715067812762" duration="431000" />
      <workItem from="1715407880695" duration="460000" />
      <workItem from="1715410078040" duration="20000" />
      <workItem from="1715421539597" duration="430000" />
      <workItem from="1715583288177" duration="3786000" />
      <workItem from="1715763909786" duration="609000" />
      <workItem from="1717040060468" duration="1215000" />
      <workItem from="1719883927100" duration="5050000" />
      <workItem from="1720404013638" duration="426000" />
      <workItem from="1720404453124" duration="1314000" />
      <workItem from="1720409083977" duration="9451000" />
      <workItem from="1721012671019" duration="11638000" />
      <workItem from="1721101082827" duration="6757000" />
      <workItem from="1721613889031" duration="8570000" />
      <workItem from="1721702611655" duration="12905000" />
      <workItem from="1722218486276" duration="14882000" />
      <workItem from="1722482817905" duration="657000" />
      <workItem from="1722823450516" duration="3753000" />
      <workItem from="1723026082828" duration="1329000" />
      <workItem from="1723079940637" duration="8288000" />
      <workItem from="1724058311414" duration="2637000" />
      <workItem from="1725954872498" duration="635000" />
      <workItem from="1726276786580" duration="17000" />
      <workItem from="1727399576494" duration="1591000" />
      <workItem from="1727589249983" duration="338000" />
      <workItem from="1737984652792" duration="916000" />
      <workItem from="1739971001190" duration="1489000" />
      <workItem from="1740623452273" duration="73000" />
      <workItem from="1740820468409" duration="501000" />
      <workItem from="1741586155144" duration="766000" />
      <workItem from="1741587017952" duration="890000" />
      <workItem from="1741680123027" duration="2053000" />
      <workItem from="1745460941724" duration="3292000" />
      <workItem from="1749863990762" duration="646000" />
      <workItem from="1755321605212" duration="1327000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>